@g = global i32 0, align 4
define i32 @func(i32 %t0) {
	%l1 = alloca i32, align 4
	%l2 = alloca i32, align 4
	store i32 %t0, i32* %l1, align 4
	%t4 = load i32, i32* @g, align 4
	%t5 = load i32, i32* %l1, align 4
	%t3 = add nsw i32 %t4, %t5
	store i32 %t3, i32* @g, align 4
	%t6 = load i32, i32* @g, align 4
	call void @putint(i32 %t6)
	%t7 = load i32, i32* @g, align 4
	store i32 %t7, i32* %l2, align 4
	%t8 = load i32, i32* %l2, align 4
	ret i32 %t8
}
define i32 @main() {
	%l0 = alloca i32, align 4
	%l1 = alloca i32, align 4
	%t2 = call i32 (...) @getint()
	store i32 %t2, i32* %l1, align 4
	%t10 = load i32, i32* %l1, align 4
	%t3 = icmp sgt i32 %t10, 10
	br i1 %t3, label %.L6, label %.L4
.L6:
	%t11 = load i32, i32* %l1, align 4
	%t4 = icmp ne i32 %t11, 0
	br i1 %t4, label %.L3, label %.L4
.L3:
	store i32 1, i32* %l1, align 4
	br label %.L5
.L4:
	store i32 0, i32* %l1, align 4
	br label %.L5
.L5:
	%t5 = call i32 (...) @getint()
	store i32 %t5, i32* %l1, align 4
	%t12 = load i32, i32* %l1, align 4
	%t6 = icmp sgt i32 %t12, 11
	br i1 %t6, label %.L10, label %.L8
.L10:
	%t13 = load i32, i32* %l1, align 4
	%t7 = call i32 @func(i32 %t13)
	%t8 = icmp ne i32 %t7, 0
	br i1 %t8, label %.L7, label %.L8
.L7:
	store i32 1, i32* %l1, align 4
	br label %.L9
.L8:
	store i32 0, i32* %l1, align 4
	br label %.L9
.L9:
	%t9 = call i32 (...) @getint()
	store i32 %t9, i32* %l1, align 4
	store i32 0, i32* %l0, align 4
	%t14 = load i32, i32* %l0, align 4
	ret i32 %t14
}
declare i32 @getint(...);
declare i32 @getch(...);
declare void @putint(i32);
declare void @putch(i32);
declare void @putstr(i8*);
declare i32 @getarray(i32*);
declare void @putarray(i32, i32*);
declare float @getfloat(...);
declare void @putfloat(float);
declare i32 @getfarray(float*);
declare void @putfarray(i32, float*);
