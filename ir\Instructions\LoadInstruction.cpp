///
/// @file LoadInstruction.cpp
/// @brief 指针读取（解引用）load指令实现
///

#include <string>
#include "LoadInstruction.h"
#include "Function.h"

/// @brief Load 指令构造函数
LoadInstruction::LoadInstruction(Function * func, Value * addr, Type * loadType)
    : Instruction(func, IRInstOperator::IRINST_OP_LOAD, loadType)
{
    this->addOperand(addr);
}

/// @brief 转换成字符串表示
void LoadInstruction::toString(std::string & str)
{
    Value * addr = getOperand(0);
    str = this->getIRName() + " = *" + addr->getIRName();

    int32_t regId;
    int64_t offset;
    if (addr->getMemoryAddr(&regId, &offset)) {
        str += " ; R" + std::to_string(regId) + "[" + std::to_string(offset) + "]";
    } else if (addr->getRegId() != -1) {
        str += " ; R" + std::to_string(addr->getRegId());
    }
}
