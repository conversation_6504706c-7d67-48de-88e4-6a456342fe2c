/// @file StoreInstruction.cpp
/// @brief 内存写入 STORE 指令实现

#include "StoreInstruction.h"
#include "VoidType.h"
#include "Function.h"

StoreInstruction::StoreInstruction(Function * func, Value * addr, Value * val)
    : Instruction(func, IRInstOperator::IRINST_OP_STORE, VoidType::getType())
{
    this->addOperand(addr); // operand(0): 指针地址
    this->addOperand(val);  // operand(1): 要写入的值
}

void StoreInstruction::toString(std::string & str)
{
    Value * addr = getOperand(0);
    Value * val = getOperand(1);

    str = "*" + addr->getIRName() + " = " + val->getIRName();

    int32_t regId;
    int64_t offset;
    if (addr->getMemoryAddr(&regId, &offset)) {
        str += " ; R" + std::to_string(regId) + "[" + std::to_string(offset) + "]";
    } else if (addr->getRegId() != -1) {
        str += " ; R" + std::to_string(addr->getRegId());
    }
}
