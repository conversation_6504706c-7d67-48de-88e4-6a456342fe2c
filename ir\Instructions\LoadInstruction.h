///
/// @file LoadInstruction.h
/// @brief 读取指针所指地址中的数据（load）指令
///

#pragma once

#include "Instruction.h"

///
/// @brief 指针解引用读取指令（load）
/// 例如：%r = *%ptr，或语义上：%r = load i32, i32* %ptr
///
class LoadInstruction : public Instruction {

public:
    /// @brief Load 指令构造函数
    /// @param func 所属函数
    /// @param addr 需要解引用的地址（应为指针类型）
    /// @param loadType 解引用后的值的类型
    LoadInstruction(Function * func, Value * addr, Type * loadType);

    /// @brief 转换成字符串
    void toString(std::string & str) override;
};
