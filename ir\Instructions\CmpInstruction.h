///
/// @file CmpInstruction.h
/// @brief 比较指令，如 <, >, <=, >=, ==, !=
///
/// <AUTHOR>
/// @version 1.0
/// @date 2025-05-13
///
/// @copyright Copyright (c) 2025
///
#pragma once

#include "Instruction.h"

/// @brief 比较运算指令
class CmpInstruction : public Instruction {
public:
    /// @brief 构造函数
    /// @param _func 当前函数
    /// @param _op 比较操作符，如 IRINST_OP_CMP_LT 等
    /// @param _srcVal1 源操作数1
    /// @param _srcVal2 源操作数2
    CmpInstruction(Function * _func, IRInstOperator _op, Value * _srcVal1, Value * _srcVal2);

    /// @brief 构造函数重载，可自定义结果类型
    /// @param _func 当前函数
    /// @param _op 比较操作符，如 IRINST_OP_CMP_LT 等
    /// @param _srcVal1 源操作数1
    /// @param _srcVal2 源操作数2
    /// @param _type 结果类型
    CmpInstruction(Function * _func, IRInstOperator _op, Value * _srcVal1, Value * _srcVal2, Type * _type);

    /// @brief 转换成字符串
    /// @param str 转换后的字符串
    void toString(std::string & str) override;
};
