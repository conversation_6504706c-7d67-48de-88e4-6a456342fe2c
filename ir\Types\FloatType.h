#pragma once

#include "Type.h"

/// @brief 单精度浮点类型
class FloatType : public Type {
private:
    /// 私有构造函数，外部只能通过 getTypeFloat() 获取单例
    FloatType() : Type(FloatTyID)
    {}

public:
    FloatType(const FloatType &) = delete;
    FloatType & operator=(const FloatType &) = delete;

    /// @brief 获取单精度浮点类型的唯一实例
    static FloatType * getTypeFloat()
    {
        static FloatType instance;
        return &instance;
    }

    /// @brief 返回 IR 中的标识
    [[nodiscard]] std::string toString() const override
    {
        return "float";
    }

    /// @brief 类型检查：是否为浮点类型
    [[nodiscard]] bool isFloatType() const override
    {
        return true;
    }
};
