///
/// @file CmpInstruction.cpp
/// @brief 比较运算指令实现
///
/// <AUTHOR>
/// @version 1.0
/// @date 2025-05-13
///

#include "CmpInstruction.h"
#include "Types/IntegerType.h"

/// @brief 构造函数，结果类型固定为 IntegerType::getTypeInt()
/// @param _func 当前函数
/// @param _op 比较操作符，如 IRINST_OP_CMP_LT 等
/// @param _srcVal1 源操作数1
/// @param _srcVal2 源操作数2
CmpInstruction::CmpInstruction(Function * _func, IRInstOperator _op, Value * _srcVal1, Value * _srcVal2)
    : Instruction(_func, _op, IntegerType::getTypeBool())
{
    addOperand(_srcVal1);
    addOperand(_srcVal2);
}

/// @brief 构造函数重载，可自定义结果类型
/// @param _func 当前函数
/// @param _op 比较操作符，如 IRINST_OP_CMP_LT 等
/// @param _srcVal1 源操作数1
/// @param _srcVal2 源操作数2
/// @param _type 结果类型
CmpInstruction::CmpInstruction(Function * _func, IRInstOperator _op, Value * _srcVal1, Value * _srcVal2, Type * _type)
    : Instruction(_func, _op, _type)
{
    addOperand(_srcVal1);
    addOperand(_srcVal2);
}

/// @brief 转换成字符串
/// @param str 转换后的字符串
void CmpInstruction::toString(std::string & str)
{
    Value * lhs = getOperand(0);
    Value * rhs = getOperand(1);

    std::string cmpOp;
    switch (op) {
        case IRInstOperator::IRINST_OP_CMP_LT:
            cmpOp = "lt";
            break;
        case IRInstOperator::IRINST_OP_CMP_GT:
            cmpOp = "gt";
            break;
        case IRInstOperator::IRINST_OP_CMP_LE:
            cmpOp = "le";
            break;
        case IRInstOperator::IRINST_OP_CMP_GE:
            cmpOp = "ge";
            break;
        case IRInstOperator::IRINST_OP_CMP_EQ:
            cmpOp = "eq";
            break;
        case IRInstOperator::IRINST_OP_CMP_NE:
            cmpOp = "ne";
            break;
        default:
            Instruction::toString(str);
            return;
    }

    // 示例："%t3 = cmp lt %t1,%t2"
    str = getIRName() + " = icmp " + cmpOp + " " + lhs->getIRName() + "," + rhs->getIRName();
}
