// ArrayType.h
#pragma once
#include <vector>
#include <string>
#include "Type.h"

// ArrayType 专门表示“数组类型”，例如 i32[10][15]
class ArrayType : public Type {
public:
    // elemTy: 元素类型（比如 IntegerType::getTypeInt()）
    // dims:   每一级的维度长度，如 {10,15}
    ArrayType(Type * elemTy, const std::vector<int> & dims) : Type(ArrayTyID), elementType(elemTy), dimensions(dims)
    {}

    // 返回元素类型，例如 i32
    [[nodiscard]] Type * getElementType() const
    {
        return elementType;
    }

    // 返回维度列表，例如 {10,15}
    [[nodiscard]] const std::vector<int> & getDimensions() const
    {
        return dimensions;
    }

    // 数组类型字符串形式，例如 "i32[10][15]"
    [[nodiscard]] std::string toString() const override
    {
        std::string str = elementType->toString();
        for (int dim: dimensions) {
            str += "[" + std::to_string(dim) + "]";
        }
        return str;
    }

    // 数组类型所占的内存字节数：elementType->getSize() * (product of all dims)
    [[nodiscard]] int32_t getSize() const override
    {
        int32_t elemSize = elementType->getSize();
        int32_t total = 1;
        for (int d: dimensions) {
            total *= d;
        }
        return elemSize * total;
    }

    // 修改指定维度的值
    void setDimension(size_t index, int value)
    {
        if (index < dimensions.size()) {
            dimensions[index] = value;
        }
    }

private:
    Type * elementType;
    std::vector<int> dimensions;
};
