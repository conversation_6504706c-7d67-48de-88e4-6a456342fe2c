///
/// @file Antlr4CSTVisitor.cpp
/// @brief Antlr4的具体语法树的遍历产生AST
/// <AUTHOR> (<EMAIL>)
/// @version 1.1
/// @date 2024-11-23
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-09-29 <td>1.0     <td>zenglj  <td>新建
/// <tr><td>2024-11-23 <td>1.1     <td>zenglj  <td>表达式版增强
/// </table>
///
#include <cstdlib>
#include <string>

#include "Antlr4CSTVisitor.h"
#include "AST.h"
#include "AttrType.h"

#define Instanceof(res, type, var) auto res = dynamic_cast<type>(var)

/// @brief 构造函数
CSTVisitor::MiniCCSTVisitor()
{}

/// @brief 析构函数
MiniCCSTVisitor::~MiniCCSTVisitor()
{}

/// @brief 遍历CST产生AST
/// @param root CST语法树的根结点
/// @return AST的根节点
ast_node * MiniCCSTVisitor::run(MiniCParser::CompileUnitContext * root)
{
    return std::any_cast<ast_node *>(visitCompileUnit(root));
}

/// @brief 非终结运算符compileUnit的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitCompileUnit(MiniCParser::CompileUnitContext * ctx)
{
    // compileUnit: (funcDef | varDecl)* EOF

    // 请注意这里必须先遍历全局变量后遍历函数。肯定可以确保全局变量先声明后使用的规则，但有些情况却不能检查出。
    // 事实上可能函数A后全局变量B后函数C，这时在函数A中是不能使用变量B的，需要报语义错误，但目前的处理不会。
    // 因此在进行语义检查时，可能追加检查行号和列号，如果函数的行号/列号在全局变量的行号/列号的前面则需要报语义错误
    // TODO 请追加实现。

    ast_node * temp_node;
    ast_node * compileUnitNode = create_contain_node(ast_operator_type::AST_OP_COMPILE_UNIT);

    // 可能多个变量，因此必须循环遍历
    for (auto varCtx: ctx->varDecl()) {

        // 变量函数定义
        temp_node = std::any_cast<ast_node *>(visitVarDecl(varCtx));
        (void) compileUnitNode->insert_son_node(temp_node);
    }

    // 可能有多个函数，因此必须循环遍历
    for (auto funcCtx: ctx->funcDef()) {

        // 变量函数定义
        temp_node = std::any_cast<ast_node *>(visitFuncDef(funcCtx));
        (void) compileUnitNode->insert_son_node(temp_node);
    }

    return compileUnitNode;
}

/// @brief 函数定义：支持 basicType ID '(' formalParamList? ')' block
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitFuncDef(MiniCParser::FuncDefContext * ctx)
{
    // 1) 先拿返回类型
    type_attr retType = std::any_cast<type_attr>(visitBasicType(ctx->basicType()));
    // 2) 再拿函数名
    char * name = strdup(ctx->T_ID()->getText().c_str());
    var_id_attr fid{name, (int64_t) ctx->T_ID()->getSymbol()->getLine()};

    // 3) 形参列表（可选）
    ast_node * paramsNode = nullptr;
    if (ctx->formalParamList()) {
        paramsNode = std::any_cast<ast_node *>(visitFormalParamList(ctx->formalParamList()));
    }

    // 4) 函数体
    ast_node * body = std::any_cast<ast_node *>(visitBlock(ctx->block()));

    // 5) 构造 AST：create_func_def(返回类型, id, 形参节点, 函数体)
    //    create_func_def 会接管 name 字符串并在内部 free 掉
    return create_func_def(retType, fid, body, paramsNode);
}

/// @brief 形参列表：basicType varDef (',' basicType varDef)*
std::any MiniCCSTVisitor::visitFormalParamList(MiniCParser::FormalParamListContext * ctx)
{
    // 创建形参列表节点
    ast_node * paramsNode = create_contain_node(ast_operator_type::AST_OP_FUNC_FORMAL_PARAMS);

    // 循环处理每个形参定义
    for (size_t i = 0; i < ctx->varDef().size(); ++i) {
        type_attr tattr = std::any_cast<type_attr>(visitBasicType(ctx->basicType(i)));
        ast_node * varDefNode = std::any_cast<ast_node *>(visitVarDef(ctx->varDef(i)));

        // 检查是否是数组形参
        // 如果是数组形参（如 int arr[] 或 int arr[10]），则修改第一个维度为 0。
        // 在 C 语言中，函数参数中的数组会退化为指针，所以第一维可以忽略（设为 0）。
        ast_node * dimsNode = nullptr;
        if (varDefNode->sons.size() >= 2 && varDefNode->sons[1]->node_type == ast_operator_type::AST_OP_ARRAY_DIM) {
            dimsNode = varDefNode->sons[1];
            // 修改第一维为 0
            if (!dimsNode->sons.empty()) {
                dimsNode->sons[0]->integer_val = 0;
            }
        }

        // 创建类型节点
        ast_node * typeNode = create_type_node(tattr);

        // 创建形参节点
        ast_node * paramNode = ast_node::New(ast_operator_type::AST_OP_PARAM, typeNode, varDefNode, nullptr);
        paramsNode->insert_son_node(paramNode);
    }

    return paramsNode;
}

/// @brief 非终结运算符block的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitBlock(MiniCParser::BlockContext * ctx)
{
    // 识别的文法产生式：block : T_L_BRACE blockItemList? T_R_BRACE';
    if (!ctx->blockItemList()) {
        // 语句块没有语句

        // 为了方便创建一个空的Block节点
        return create_contain_node(ast_operator_type::AST_OP_BLOCK);
    }

    // 语句块含有语句

    // 内部创建Block节点，并把语句加入，这里不需要创建Block节点
    return visitBlockItemList(ctx->blockItemList());
}

/// @brief 非终结运算符blockItemList的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitBlockItemList(MiniCParser::BlockItemListContext * ctx)
{
    // 识别的文法产生式：blockItemList : blockItem +;
    // 正闭包 循环 至少一个blockItem
    auto block_node = create_contain_node(ast_operator_type::AST_OP_BLOCK);

    for (auto blockItemCtx: ctx->blockItem()) {

        // 非终结符，需遍历
        auto blockItem = std::any_cast<ast_node *>(visitBlockItem(blockItemCtx));

        // 插入到块节点中
        (void) block_node->insert_son_node(blockItem);
    }

    return block_node;
}

///
/// @brief 非终结运算符blockItem的遍历
/// @param ctx CST上下文
///
std::any MiniCCSTVisitor::visitBlockItem(MiniCParser::BlockItemContext * ctx)
{
    // 识别的文法产生式：blockItem : statement | varDecl
    if (ctx->statement()) {
        // 语句识别

        return visitStatement(ctx->statement());
    } else if (ctx->varDecl()) {
        return visitVarDecl(ctx->varDecl());
    }

    return nullptr;
}

/// @brief 非终结运算符statement中的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitStatement(MiniCParser::StatementContext * ctx)
{
    // 识别的文法产生式：statement: T_ID T_ASSIGN expr T_SEMICOLON  # assignStatement
    // | T_RETURN expr T_SEMICOLON # returnStatement
    // | block  # blockStatement
    // | expr ? T_SEMICOLON #expressionStatement
    // | T_IF T_L_PAREN expr T_R_PAREN statement(T_ELSE statement) ? #ifStatement
    // | T_WHILE T_L_PAREN expr T_R_PAREN statement					# whileStatement;
    if (Instanceof(assignCtx, MiniCParser::AssignStatementContext *, ctx)) {
        return visitAssignStatement(assignCtx);
    } else if (Instanceof(returnCtx, MiniCParser::ReturnStatementContext *, ctx)) {
        return visitReturnStatement(returnCtx);
    } else if (Instanceof(blockCtx, MiniCParser::BlockStatementContext *, ctx)) {
        return visitBlockStatement(blockCtx);
    } else if (Instanceof(exprCtx, MiniCParser::ExpressionStatementContext *, ctx)) {
        return visitExpressionStatement(exprCtx);
    } else if (Instanceof(ifCtx, MiniCParser::IfStatementContext *, ctx)) {
        return visitIfStatement(ifCtx);
    } else if (Instanceof(whileCtx, MiniCParser::WhileStatementContext *, ctx)) {
        return visitWhileStatement(whileCtx);
    } else if (Instanceof(breakCtx, MiniCParser::BreakStatementContext *, ctx)) {
        return visitBreakStatement(breakCtx);
    } else if (Instanceof(contCtx, MiniCParser::ContinueStatementContext *, ctx)) {
        return visitContinueStatement(contCtx);
    } else if (Instanceof(forCtx, MiniCParser::ForStatementContext *, ctx)) {
        return visitForStatement(forCtx);
    }
    return nullptr;
}

/// @brief 解析 break 语句
std::any MiniCCSTVisitor::visitBreakStatement(MiniCParser::BreakStatementContext * ctx)
{
    // 直接生成 AST_OP_BREAK 节点，无子节点
    return ast_node::New(ast_operator_type::AST_OP_BREAK, nullptr);
}

/// @brief 解析 continue 语句
std::any MiniCCSTVisitor::visitContinueStatement(MiniCParser::ContinueStatementContext * ctx)
{
    // 直接生成 AST_OP_CONTINUE 节点，无子节点
    return ast_node::New(ast_operator_type::AST_OP_CONTINUE, nullptr);
}

std::any MiniCCSTVisitor::visitIfStatement(MiniCParser::IfStatementContext * ctx)
{
    // 识别文法：T_IF T_L_PAREN expr T_R_PAREN statement (T_ELSE statement)?
    ast_node * ifNode = create_contain_node(ast_operator_type::AST_OP_IF);

    // 1. 条件表达式
    if (ctx->expr()) {
        ast_node * condNode = std::any_cast<ast_node *>(visitExpr(ctx->expr()));
        ifNode->insert_son_node(condNode);
    }

    // 2. then 分支
    if (!ctx->statement().empty()) {
        ast_node * thenNode = std::any_cast<ast_node *>(visitStatement(ctx->statement(0)));
        if (thenNode) {
            ifNode->insert_son_node(thenNode);
        } else {
            // 如果是空语句，则插入一个明确表示空语句的占位节点（可选）
            // 或插入一个空block节点，确保AST完整
            ast_node * emptyStmt = create_contain_node(ast_operator_type::AST_OP_BLOCK);
            ifNode->insert_son_node(emptyStmt);
        }
    }

    // 3. else 分支（可选）
    if (ctx->statement().size() > 1) {
        ast_node * elseNode = std::any_cast<ast_node *>(visitStatement(ctx->statement(1)));
        ifNode->insert_son_node(elseNode);
    }

    // 4. 构造 AST_OP_IF 节点，孩子为 cond, then, else（如果 elseNode 为 nullptr 则只有前两个孩子）
    return ifNode;
}

/// @brief 非终结运算符statement中的returnStatement的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitReturnStatement(MiniCParser::ReturnStatementContext * ctx)
{
    if (ctx->expr()) {
        // return expr;
        ast_node * exprNode = std::any_cast<ast_node *>(visitExpr(ctx->expr()));
        return create_contain_node(ast_operator_type::AST_OP_RETURN, exprNode);
    } else {
        // return;
        return create_contain_node(ast_operator_type::AST_OP_RETURN);
    }
}

/// @brief 非终结运算符expr的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitExpr(MiniCParser::ExprContext * ctx)
{
    return visitAssignExp(ctx->assignExp());
}

std::any MiniCCSTVisitor::visitAssignExp(MiniCParser::AssignExpContext * ctx)
{
    if (!ctx->T_ASSIGN()) {
        // 如果没有赋值操作，直接返回逻辑或表达式
        return visitLogicalOrExp(ctx->logicalOrExp());
    }

    // 处理赋值操作
    ast_node * lvalNode = std::any_cast<ast_node *>(visitLogicalOrExp(ctx->logicalOrExp()));
    ast_node * rvalNode = std::any_cast<ast_node *>(visitAssignExp(ctx->assignExp()));

    // 创建 AST_OP_ASSIGN 节点
    return ast_node::New(ast_operator_type::AST_OP_ASSIGN, lvalNode, rvalNode, nullptr);
}

std::any MiniCCSTVisitor::visitLogicalOrExp(MiniCParser::LogicalOrExpContext * ctx)
{
    // logicalOrExp: logicalAndExp ( '||' logicalAndExp )*
    ast_node * left = std::any_cast<ast_node *>(visitLogicalAndExp(ctx->logicalAndExp(0)));
    for (int i = 1; i < ctx->logicalAndExp().size(); ++i) {
        ast_node * right = std::any_cast<ast_node *>(visitLogicalAndExp(ctx->logicalAndExp(i)));
        left = ast_node::New(ast_operator_type::AST_OP_OR, left, right, nullptr);
    }
    return left;
}

std::any MiniCCSTVisitor::visitLogicalAndExp(MiniCParser::LogicalAndExpContext * ctx)
{
    // logicalAndExp: relationalExp ( '&&' relationalExp )*
    ast_node * left = std::any_cast<ast_node *>(visitRelationalExp(ctx->relationalExp(0)));
    for (int i = 1; i < ctx->relationalExp().size(); ++i) {
        ast_node * right = std::any_cast<ast_node *>(visitRelationalExp(ctx->relationalExp(i)));
        left = ast_node::New(ast_operator_type::AST_OP_AND, left, right, nullptr);
    }
    return left;
}

std::any MiniCCSTVisitor::visitRelationalExp(MiniCParser::RelationalExpContext * ctx)
{
    // 先生成左侧的 addExp
    ast_node * left = std::any_cast<ast_node *>(visitAddExp(ctx->addExp(0)));
    // 如果没有比较符，就直接返回左侧的算术子树
    if (!ctx->T_LT() && !ctx->T_GT() && !ctx->T_LE() && !ctx->T_GE() && !ctx->T_EQ() && !ctx->T_NE()) {
        return left;
    }
    // 生成右侧的 addExp
    ast_node * right = std::any_cast<ast_node *>(visitAddExp(ctx->addExp(1)));

    // 决定是哪种比较
    ast_operator_type cmpOp;
    if (ctx->T_LT())
        cmpOp = ast_operator_type::AST_OP_CMP_LT;
    else if (ctx->T_GT())
        cmpOp = ast_operator_type::AST_OP_CMP_GT;
    else if (ctx->T_LE())
        cmpOp = ast_operator_type::AST_OP_CMP_LE;
    else if (ctx->T_GE())
        cmpOp = ast_operator_type::AST_OP_CMP_GE;
    else if (ctx->T_EQ())
        cmpOp = ast_operator_type::AST_OP_CMP_EQ;
    else /* ctx->T_NE() */
        cmpOp = ast_operator_type::AST_OP_CMP_NE;

    // 只生成比较节点
    return ast_node::New(cmpOp, left, right, nullptr);
}

std::any MiniCCSTVisitor::visitAssignStatement(MiniCParser::AssignStatementContext * ctx)
{
    // 识别文法产生式：assignStatement: lVal T_ASSIGN expr T_SEMICOLON

    // 赋值左侧左值Lval遍历产生节点
    auto lvalNode = std::any_cast<ast_node *>(visitLVal(ctx->lVal()));

    // 赋值右侧expr遍历
    auto exprNode = std::any_cast<ast_node *>(visitExpr(ctx->expr()));

    // 创建一个AST_OP_ASSIGN类型的中间节点，孩子为Lval和Expr
    return ast_node::New(ast_operator_type::AST_OP_ASSIGN, lvalNode, exprNode, nullptr);
}

std::any MiniCCSTVisitor::visitBlockStatement(MiniCParser::BlockStatementContext * ctx)
{
    // 识别文法产生式 blockStatement: block

    return visitBlock(ctx->block());
}

std::any MiniCCSTVisitor::visitAddExp(MiniCParser::AddExpContext * ctx)
{
    // 识别的文法产生式：addExp : unaryExp (addOp unaryExp)*;

    if (ctx->addOp().empty()) {

        // 没有addOp运算符，则说明闭包识别为0，只识别了第一个非终结符unaryExp
        return visitMulExp(ctx->mulExp()[0]);
    }

    ast_node *left, *right;

    // 存在addOp运算符，自
    auto opsCtxVec = ctx->addOp();

    // 有操作符，肯定会进循环，使得right设置正确的值
    for (int k = 0; k < (int) opsCtxVec.size(); k++) {

        // 获取运算符
        ast_operator_type op = std::any_cast<ast_operator_type>(visitAddOp(opsCtxVec[k]));

        if (k == 0) {

            // 左操作数
            left = std::any_cast<ast_node *>(visitMulExp(ctx->mulExp()[k]));
        }

        // 右操作数
        right = std::any_cast<ast_node *>(visitMulExp(ctx->mulExp()[k + 1]));

        // 新建结点作为下一个运算符的右操作符
        left = ast_node::New(op, left, right, nullptr);
    }

    return left;
}

/// @brief 非终结运算符addOp的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitAddOp(MiniCParser::AddOpContext * ctx)
{
    // 识别的文法产生式：addOp : T_ADD | T_SUB

    if (ctx->T_ADD()) {
        return ast_operator_type::AST_OP_ADD;
    } else {
        return ast_operator_type::AST_OP_SUB;
    }
}

std::any MiniCCSTVisitor::visitUnaryExp(MiniCParser::UnaryExpContext * ctx)
{
    // 处理前缀 ++i 或 --i
    if (ctx->T_ADDADD() || ctx->T_SUBSUB()) {
        ast_node * val = std::any_cast<ast_node *>(visitUnaryExp(ctx->unaryExp()));
        ast_operator_type op = ctx->T_ADDADD() ? ast_operator_type::AST_OP_PRE_INC : ast_operator_type::AST_OP_PRE_DEC;
        return ast_node::New(op, val, nullptr, nullptr);
    }
    // 处理后缀 i++ 或 i--
    if (ctx->T_ADDADD() || ctx->T_SUBSUB()) {
        ast_node * val = std::any_cast<ast_node *>(visitUnaryExp(ctx->unaryExp()));
        ast_operator_type op =
            ctx->T_ADDADD() ? ast_operator_type::AST_OP_POST_INC : ast_operator_type::AST_OP_POST_DEC;
        return ast_node::New(op, val, nullptr, nullptr);
    }
    // 1) 前缀递归：'-' 或 '!'
    if (ctx->T_SUB() || ctx->T_NOT()) {
        ast_node * child = std::any_cast<ast_node *>(visitUnaryExp(ctx->unaryExp()));
        if (ctx->T_SUB()) {
            // 生成 (0 - E)
            int64_t ln = ctx->T_SUB()->getSymbol()->getLine();
            ast_node * zero = ast_node::New(digit_int_attr{0, ln});
            return ast_node::New(ast_operator_type::AST_OP_SUB, zero, child, nullptr);
        } else {
            // 生成 !E
            return ast_node::New(ast_operator_type::AST_OP_NOT, child, nullptr, nullptr);
        }
    }
    // 2) 函数调用
    if (ctx->T_ID()) {
        ast_node * fn = ast_node::New(ctx->T_ID()->getText(), (int64_t) ctx->T_ID()->getSymbol()->getLine());
        ast_node * params = nullptr;
        if (ctx->realParamList()) {
            params = std::any_cast<ast_node *>(visitRealParamList(ctx->realParamList()));
        }
        return create_func_call(fn, params);
    }
    // 3) 括号 / 字面量 / 变量
    return visitPrimaryExp(ctx->primaryExp());
}


// minic.g4  词法规则// 词法规则支持不同进制的数字
// T_DIGIT : [0-9]+ 
//         | '0'[xX][0-9a-fA-F]+ 
//         | '0'[0-7]+
std::any MiniCCSTVisitor::visitPrimaryExp(MiniCParser::PrimaryExpContext * ctx)
{
    // 识别文法产生式 primaryExp: T_L_PAREN expr T_R_PAREN | T_DIGIT | lVal;

    ast_node * node = nullptr;

    if (ctx->T_DIGIT()) {
        // 无符号整型字面量
        // 识别 primaryExp: T_DIGIT

        // 无符号整型字面量（支持 0/0123/0x1A3F 三种形式）
        const std::string txt = ctx->T_DIGIT()->getText();
        // std::stoul with base=0 会根据前缀自动选择进制
        uint32_t val = static_cast<uint32_t>(std::stoul(txt, nullptr, 0));
        int64_t lineNo = (int64_t) ctx->T_DIGIT()->getSymbol()->getLine();
        node = ast_node::New(digit_int_attr{val, lineNo});
    } else if (ctx->lVal()) {
        // 具有左值的表达式
        // 识别 primaryExp: lVal
        node = std::any_cast<ast_node *>(visitLVal(ctx->lVal()));
    } else if (ctx->expr()) {
        // 带有括号的表达式
        // primaryExp: T_L_PAREN expr T_R_PAREN
        node = std::any_cast<ast_node *>(visitExpr(ctx->expr()));
    }

    return node;
}

std::any MiniCCSTVisitor::visitLVal(MiniCParser::LValContext * ctx)
{
    auto varId = ctx->T_ID()->getText();
    int64_t lineNo = (int64_t) ctx->T_ID()->getSymbol()->getLine();
    ast_node * idNode = ast_node::New(varId, lineNo);

    // 如果没有下标，返回标识符本身
    if (!ctx->indexes())
        return idNode;

    // 否则，处理下标表达式
    ast_node * indexesNode = std::any_cast<ast_node *>(visitIndexes(ctx->indexes()));

    // 创建 AST_OP_ARRAY_ACCESS 节点：孩子是 idNode + indexesNode
    return ast_node::New(ast_operator_type::AST_OP_ARRAY_ACCESS, idNode, indexesNode, nullptr);
}

std::any MiniCCSTVisitor::visitVarDecl(MiniCParser::VarDeclContext * ctx)
{
    // varDecl: basicType varDef (',' varDef)* ';'
    ast_node * stmtNode = create_contain_node(ast_operator_type::AST_OP_DECL_STMT);
    type_attr typeAttr = std::any_cast<type_attr>(visitBasicType(ctx->basicType()));

    for (auto & varCtx: ctx->varDef()) {
        // varDefNode 是我们新返回的 AST_OP_VAR_DEF
        ast_node * varDefNode = std::any_cast<ast_node *>(visit(varCtx));

        // 1) 创建类型节点
        ast_node * typeNode = create_type_node(typeAttr);

        // 2) 创建 VAR_DECL 节点，孩子：typeNode, varDefNode
        ast_node * declNode = ast_node::New(ast_operator_type::AST_OP_VAR_DECL, typeNode, varDefNode, nullptr);
        stmtNode->insert_son_node(declNode);
    }

    return stmtNode;
}

std::any MiniCCSTVisitor::visitVarDef(MiniCParser::VarDefContext *ctx) {
    // 从ID Token创建变量标识符节点
    auto idToken = ctx->T_ID();
    ast_node *identifier =
        ast_node::New(idToken->getText(), (int64_t)idToken->getSymbol()->getLine());

    // 创建变量定义节点，并将标识符作为其子节点
    ast_node *defNode = create_contain_node(ast_operator_type::AST_OP_VAR_DEF, identifier);

    // 添加可选的数组维度
    if (auto dims = ctx->dims()) {
        defNode->insert_son_node(std::any_cast<ast_node *>(visitDims(dims)));
    }

    // 添加可选的初始化表达式
    if (auto expr = ctx->expr()) {
        defNode->insert_son_node(std::any_cast<ast_node *>(visitExpr(expr)));
    }

    return defNode;
}

std::any MiniCCSTVisitor::visitBasicType(MiniCParser::BasicTypeContext *ctx) {
    // basicType: T_INT | T_VOID;
    type_attr attr{BasicType::TYPE_VOID, -1};
    if (ctx->T_INT()) {
        attr.type = BasicType::TYPE_INT;
        attr.lineno = (int64_t) ctx->T_INT()->getSymbol()->getLine();
    } else if (ctx->T_VOID()) {
        attr.type = BasicType::TYPE_VOID;
        attr.lineno = (int64_t) ctx->T_VOID()->getSymbol()->getLine();
    }

    return attr;
}

std::any MiniCCSTVisitor::visitRealParamList(MiniCParser::RealParamListContext * ctx)
{
    // 识别的文法产生式：realParamList : expr (T_COMMA expr)*;

    // 创建实参列表节点
    auto paramListNode = create_contain_node(ast_operator_type::AST_OP_FUNC_REAL_PARAMS);

    //遍历所有实参表达式，将它们添加到实参列表节点中
    for (auto paramCtx: ctx->expr()) {

        auto paramNode = std::any_cast<ast_node *>(visitExpr(paramCtx));

        paramListNode->insert_son_node(paramNode);
    }

    return paramListNode;
}

std::any MiniCCSTVisitor::visitExpressionStatement(MiniCParser::ExpressionStatementContext * ctx)
{
    // 识别文法产生式  expr ? T_SEMICOLON #expressionStatement;
    if (ctx->expr()) {
        // 表达式语句

        // 遍历expr非终结符，创建表达式节点后返回
        return visitExpr(ctx->expr());
    } else {
        // 空语句
        // 显式封装为 ast_node* 类型的空值
        return static_cast<ast_node *>(nullptr);
    }
}

std::any MiniCCSTVisitor::visitMulExp(MiniCParser::MulExpContext * ctx)
{
    // 识别文法产生式：mulExp: unaryExp (mulOp unaryExp)*

    // 如果没有mulOp运算符，则只包含一个unaryExp
    if (ctx->mulOp().empty()) {
        return visitUnaryExp(ctx->unaryExp()[0]);
    }

    // 至少有一个操作符，左结合：先解析第一个unaryExp作为left
    ast_node * left = std::any_cast<ast_node *>(visitUnaryExp(ctx->unaryExp()[0]));

    // 依次处理每一个mulOp和后面的unaryExp
    for (int i = 0; i < (int) ctx->mulOp().size(); ++i) {
        // 取得操作符对应的枚举
        ast_operator_type op = std::any_cast<ast_operator_type>(visitMulOp(ctx->mulOp(i)));
        // 取得右侧操作数
        ast_node * right = std::any_cast<ast_node *>(visitUnaryExp(ctx->unaryExp()[i + 1]));
        // 构造新的子树，作为下一次的left
        left = ast_node::New(op, left, right, nullptr);
    }

    return left;
}

std::any MiniCCSTVisitor::visitMulOp(MiniCParser::MulOpContext * ctx)
{
    // 识别文法产生式：mulOp: T_MUL | T_DIV | T_MOD

    if (ctx->T_MUL()) {
        return ast_operator_type::AST_OP_MUL;
    } else if (ctx->T_DIV()) {
        return ast_operator_type::AST_OP_DIV;
    } else {
        // ctx->T_MOD()
        return ast_operator_type::AST_OP_MOD;
    }
}

std::any MiniCCSTVisitor::visitWhileStatement(MiniCParser::WhileStatementContext * ctx)
{
    // 1) 条件
    ast_node * cond = std::any_cast<ast_node *>(visitExpr(ctx->expr()));
    // 2) 循环体（statement 上可以是单条或 block）
    ast_node * body = std::any_cast<ast_node *>(visitStatement(ctx->statement()));
    // 3) 构造 AST_OP_WHILE 节点
    return ast_node::New(ast_operator_type::AST_OP_WHILE,
                         cond,     // 左孩子：条件
                         body,     // 右孩子：循环体
                         nullptr); // 尾部 nullptr
}

std::any MiniCCSTVisitor::visitDims(MiniCParser::DimsContext * ctx)
{
    auto dimsNode = create_contain_node(ast_operator_type::AST_OP_ARRAY_DIM);

    for (auto dimCtx: ctx->dim()) {
        auto dimNode = std::any_cast<ast_node *>(visitDim(dimCtx));
        dimsNode->insert_son_node(dimNode);
    }

    return dimsNode;
}

std::any MiniCCSTVisitor::visitDim(MiniCParser::DimContext * ctx)
{
    if (ctx->expr()) {
        return visitExpr(ctx->expr());
    } else {
        return ast_node::New("<empty_dim>", ctx->getStart()->getLine());
    }
}

std::any MiniCCSTVisitor::visitIndexes(MiniCParser::IndexesContext * ctx)
{
    auto indexesNode = create_contain_node(ast_operator_type::AST_OP_ARRAY_INDEX);

    for (auto exprCtx: ctx->expr()) {
        auto indexExpr = std::any_cast<ast_node *>(visitExpr(exprCtx));
        indexesNode->insert_son_node(indexExpr);
    }

    return indexesNode;
}

std::any MiniCCSTVisitor::visitForStatement(MiniCParser::ForStatementContext * ctx)
{
    // 创建 AST_OP_FOR 节点
    auto forNode = create_contain_node(ast_operator_type::AST_OP_FOR);

    // 处理 forInit 部分
    if (ctx->forInit()) {
        ast_node * init = std::any_cast<ast_node *>(visit(ctx->forInit()));
        forNode->insert_son_node(init);
    } else {
        // 插入占位节点，表示没有初始化部分
        forNode->insert_son_node(create_contain_node(ast_operator_type::AST_OP_EMPTY));
    }

    // 处理条件表达式部分
    if (ctx->expr()) {
        ast_node * cond = std::any_cast<ast_node *>(visit(ctx->expr()));
        forNode->insert_son_node(cond);
    } else {
        // 插入占位节点，表示没有条件表达式
        forNode->insert_son_node(create_contain_node(ast_operator_type::AST_OP_EMPTY));
    }

    // 处理 forUpdate 部分
    if (ctx->forUpdate()) {
        ast_node * update = std::any_cast<ast_node *>(visit(ctx->forUpdate()));
        forNode->insert_son_node(update);
    } else {
        // 插入占位节点，表示没有条件表达式
        forNode->insert_son_node(create_contain_node(ast_operator_type::AST_OP_EMPTY));
    }

    // 处理循环体部分
    ast_node * body = nullptr;
    if (ctx->statement()) {
        body = std::any_cast<ast_node *>(visit(ctx->statement()));
    }
    if (!body) {
        // 插入空块节点，表示循环体为空
        body = create_contain_node(ast_operator_type::AST_OP_BLOCK);
    }
    forNode->insert_son_node(body);

    return forNode;
}

std::any MiniCCSTVisitor::visitForUpdate(MiniCParser::ForUpdateContext * ctx)
{
    if (ctx->assignExp().size() == 1) {
        // 只有一个赋值表达式，直接返回
        return visitAssignExp(ctx->assignExp(0));
    }

    // 多个赋值表达式，创建 AST_OP_COMMA 节点
    ast_node * seq = create_contain_node(ast_operator_type::AST_OP_COMMA);
    for (auto assignExpCtx: ctx->assignExp()) {
        ast_node * assignNode = std::any_cast<ast_node *>(visitAssignExp(assignExpCtx));
        seq->insert_son_node(assignNode);
    }

    return seq;
}

std::any MiniCCSTVisitor::visitForInit(MiniCParser::ForInitContext * ctx)
{
    // 处理变量声明部分
    if (ctx->basicType()) {
        type_attr typeAttr = std::any_cast<type_attr>(visitBasicType(ctx->basicType()));
        ast_node * stmtNode = create_contain_node(ast_operator_type::AST_OP_DECL_STMT);

        for (auto & varCtx: ctx->varDef()) {
            // 处理每个变量定义
            ast_node * varDefNode = std::any_cast<ast_node *>(visitVarDef(varCtx));

            // 创建类型节点
            ast_node * typeNode = create_type_node(typeAttr);

            // 创建 VAR_DECL 节点，孩子：typeNode, varDefNode
            ast_node * declNode = ast_node::New(ast_operator_type::AST_OP_VAR_DECL, typeNode, varDefNode, nullptr);
            stmtNode->insert_son_node(declNode);
        }

        return stmtNode;
    }

    // 处理赋值表达式部分
    if (ctx->assignExp()) {
        return visitAssignExp(ctx->assignExp());
    }

    return nullptr; // 如果没有 forInit 部分，返回 nullptr
}
